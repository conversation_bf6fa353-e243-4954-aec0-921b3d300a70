// =============================================================================
// Session Fix Service - CarNow
// خدمة إصلاح الجلسة - كار ناو
// =============================================================================

import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod/riverpod.dart';

import '../config/session_persistence_config.dart';
import 'simple_secure_token_storage.dart';
import 'auth_interfaces.dart';

part 'session_fix_service.g.dart';

/// Service to fix session persistence issues
/// خدمة لإصلاح مشاكل استمرارية الجلسة
class SessionFixService {
  final SimpleSecureTokenStorage _tokenStorage;

  SessionFixService(this._tokenStorage);

  /// Fix session persistence by migrating to simple storage
  /// إصلاح استمرارية الجلسة بالانتقال للتخزين البسيط
  Future<SessionFixResult> fixSessionPersistence() async {
    try {
      developer.log(
        '🔧 Starting session persistence fix...',
        name: 'SessionFixService',
      );

      // Step 1: Check if we have any existing tokens
      final hasToken = await _tokenStorage.hasValidToken();
      final hasRefreshToken = await _tokenStorage.hasValidRefreshToken();

      if (hasToken || hasRefreshToken) {
        developer.log(
          '✅ Found existing valid tokens, extending session',
          name: 'SessionFixService',
        );

        // Extend the session for better user experience
        await _tokenStorage.extendSession();

        return SessionFixResult.success(
          message: 'Session extended successfully',
          hadExistingTokens: true,
        );
      }

      // Step 2: Clear any corrupted data
      await _clearCorruptedData();

      developer.log(
        '🧹 No valid tokens found, session cleared for fresh start',
        name: 'SessionFixService',
      );

      return SessionFixResult.success(
        message: 'Session cleared for fresh start',
        hadExistingTokens: false,
      );
    } catch (e) {
      developer.log(
        '❌ Session fix failed: $e',
        name: 'SessionFixService',
        error: e,
      );

      return SessionFixResult.failure(
        error: 'Session fix failed: $e',
      );
    }
  }

  /// Clear any corrupted or old data
  /// مسح أي بيانات تالفة أو قديمة
  Future<void> _clearCorruptedData() async {
    try {
      // Clear all data to start fresh
      await _tokenStorage.clearAllData();

      developer.log(
        '🧹 Cleared all authentication data',
        name: 'SessionFixService',
      );
    } catch (e) {
      developer.log(
        '❌ Failed to clear corrupted data: $e',
        name: 'SessionFixService',
        error: e,
      );
    }
  }

  /// Check session health and provide diagnostics
  /// فحص صحة الجلسة وتوفير التشخيص
  Future<SessionDiagnostics> diagnoseSession() async {
    try {
      final hasToken = await _tokenStorage.hasValidToken();
      final hasRefreshToken = await _tokenStorage.hasValidRefreshToken();
      final tokenExpiry = await _tokenStorage.getTokenExpiry();
      final lastLogin = await _tokenStorage.getLastLogin();
      final sessionData = await _tokenStorage.getSessionData();

      final diagnostics = SessionDiagnostics(
        hasValidToken: hasToken,
        hasValidRefreshToken: hasRefreshToken,
        tokenExpiry: tokenExpiry,
        lastLogin: lastLogin,
        sessionDataExists: sessionData.isNotEmpty,
        storageIntegrity: await _tokenStorage.validateStorageIntegrity(),
        isEncrypted: _tokenStorage.isEncrypted,
      );

      developer.log(
        '🔍 Session diagnostics: ${diagnostics.toString()}',
        name: 'SessionFixService',
      );

      return diagnostics;
    } catch (e) {
      developer.log(
        '❌ Session diagnostics failed: $e',
        name: 'SessionFixService',
        error: e,
      );

      return SessionDiagnostics.error(error: e.toString());
    }
  }

  /// Perform automatic session maintenance
  /// تنفيذ صيانة تلقائية للجلسة
  Future<void> performMaintenance() async {
    try {
      developer.log(
        '🔧 Performing session maintenance...',
        name: 'SessionFixService',
      );

      // Check if tokens are close to expiry
      final tokenExpiry = await _tokenStorage.getTokenExpiry();
      if (tokenExpiry != null) {
        final isCloseToExpiry = SessionPersistenceConfig.isTokenCloseToExpiry(tokenExpiry);
        
        if (isCloseToExpiry) {
          developer.log(
            '⚠️ Token is close to expiry, extending session',
            name: 'SessionFixService',
          );
          
          await _tokenStorage.extendSession();
        }
      }

      // Check last login and extend if needed
      final lastLogin = await _tokenStorage.getLastLogin();
      if (lastLogin != null) {
        final canExtend = SessionPersistenceConfig.canExtendSession(lastLogin);
        
        if (canExtend) {
          await _tokenStorage.extendSession();
          developer.log(
            '✅ Session extended based on recent activity',
            name: 'SessionFixService',
          );
        }
      }
    } catch (e) {
      developer.log(
        '❌ Session maintenance failed: $e',
        name: 'SessionFixService',
        error: e,
      );
    }
  }
}

/// Result of session fix operation
/// نتيجة عملية إصلاح الجلسة
class SessionFixResult {
  final bool success;
  final String message;
  final bool? hadExistingTokens;
  final String? error;

  const SessionFixResult._({
    required this.success,
    required this.message,
    this.hadExistingTokens,
    this.error,
  });

  factory SessionFixResult.success({
    required String message,
    bool? hadExistingTokens,
  }) {
    return SessionFixResult._(
      success: true,
      message: message,
      hadExistingTokens: hadExistingTokens,
    );
  }

  factory SessionFixResult.failure({
    required String error,
  }) {
    return SessionFixResult._(
      success: false,
      message: 'Session fix failed',
      error: error,
    );
  }

  @override
  String toString() {
    return 'SessionFixResult(success: $success, message: $message, hadExistingTokens: $hadExistingTokens, error: $error)';
  }
}

/// Session diagnostics information
/// معلومات تشخيص الجلسة
class SessionDiagnostics {
  final bool hasValidToken;
  final bool hasValidRefreshToken;
  final DateTime? tokenExpiry;
  final DateTime? lastLogin;
  final bool sessionDataExists;
  final bool storageIntegrity;
  final bool isEncrypted;
  final String? error;

  const SessionDiagnostics({
    required this.hasValidToken,
    required this.hasValidRefreshToken,
    this.tokenExpiry,
    this.lastLogin,
    required this.sessionDataExists,
    required this.storageIntegrity,
    required this.isEncrypted,
    this.error,
  });

  factory SessionDiagnostics.error({required String error}) {
    return SessionDiagnostics(
      hasValidToken: false,
      hasValidRefreshToken: false,
      sessionDataExists: false,
      storageIntegrity: false,
      isEncrypted: false,
      error: error,
    );
  }

  bool get isHealthy => hasValidToken && storageIntegrity && error == null;

  @override
  String toString() {
    return 'SessionDiagnostics('
        'hasValidToken: $hasValidToken, '
        'hasValidRefreshToken: $hasValidRefreshToken, '
        'tokenExpiry: $tokenExpiry, '
        'lastLogin: $lastLogin, '
        'sessionDataExists: $sessionDataExists, '
        'storageIntegrity: $storageIntegrity, '
        'isEncrypted: $isEncrypted, '
        'error: $error, '
        'isHealthy: $isHealthy'
        ')';
  }
}

/// Provider for session fix service
@riverpod
SessionFixService sessionFixService(Ref ref) {
  final tokenStorage = ref.watch(simpleSecureTokenStorageProvider);
  return SessionFixService(tokenStorage);
}
