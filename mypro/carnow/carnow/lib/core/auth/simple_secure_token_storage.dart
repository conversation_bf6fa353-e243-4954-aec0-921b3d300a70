// =============================================================================
// Simple Secure Token Storage - CarNow
// تخزين آمن بسيط للتوكن - كار ناو
// =============================================================================

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod/riverpod.dart';

import '../config/session_persistence_config.dart';
import 'auth_interfaces.dart';

part 'simple_secure_token_storage.g.dart';

/// Simple configuration for secure storage without complex encryption
class SimpleSecureStorageConfig {
  static const String accessTokenKey = 'carnow_simple_access_token';
  static const String refreshTokenKey = 'carnow_simple_refresh_token';
  static const String tokenExpiryKey = 'carnow_simple_token_expiry';
  static const String sessionDataKey = 'carnow_simple_session_data';
  static const String lastLoginKey = 'carnow_simple_last_login';

  static const AndroidOptions androidOptions = AndroidOptions(
    encryptedSharedPreferences: true,
    sharedPreferencesName: 'carnow_simple_prefs',
    preferencesKeyPrefix: 'carnow_simple_',
  );

  static const IOSOptions iosOptions = IOSOptions(
    groupId: 'group.com.carnow.app',
    accountName: 'carnow_simple_tokens',
    accessibility: KeychainAccessibility.first_unlock_this_device,
  );
}

/// Simple secure token storage without complex encryption
/// Uses Flutter Secure Storage's built-in encryption only
class SimpleSecureTokenStorage implements ITokenStorage {
  final FlutterSecureStorage _secureStorage;

  SimpleSecureTokenStorage({FlutterSecureStorage? secureStorage})
      : _secureStorage = secureStorage ??
            const FlutterSecureStorage(
              aOptions: SimpleSecureStorageConfig.androidOptions,
              iOptions: SimpleSecureStorageConfig.iosOptions,
            );

  // ---------------------------------------------------------------------------
  // Token Storage Operations
  // ---------------------------------------------------------------------------

  @override
  Future<void> storeToken(String token, {DateTime? expiryDate}) async {
    if (token.isEmpty) {
      throw ArgumentError('Token cannot be empty');
    }

    try {
      // Store token directly using Flutter Secure Storage's built-in encryption
      await _secureStorage.write(
        key: SimpleSecureStorageConfig.accessTokenKey,
        value: token,
      );

      // Store expiry date with extended duration for better UX
      final finalExpiryDate = expiryDate ?? SessionPersistenceConfig.getAccessTokenExpiryFromNow();
      await _secureStorage.write(
        key: SimpleSecureStorageConfig.tokenExpiryKey,
        value: finalExpiryDate.toIso8601String(),
      );

      // Update last login timestamp
      await _secureStorage.write(
        key: SimpleSecureStorageConfig.lastLoginKey,
        value: DateTime.now().toIso8601String(),
      );

      debugPrint('✅ Token stored successfully with expiry: $finalExpiryDate');
      debugPrint('📅 Token will expire in: ${finalExpiryDate.difference(DateTime.now()).inDays} days');
    } catch (e) {
      debugPrint('❌ Failed to store token: $e');
      throw Exception('Failed to store token: $e');
    }
  }

  @override
  Future<void> storeRefreshToken(String refreshToken, {DateTime? expiryDate}) async {
    if (refreshToken.isEmpty) {
      throw ArgumentError('Refresh token cannot be empty');
    }

    try {
      await _secureStorage.write(
        key: SimpleSecureStorageConfig.refreshTokenKey,
        value: refreshToken,
      );

      debugPrint('✅ Refresh token stored successfully');
    } catch (e) {
      debugPrint('❌ Failed to store refresh token: $e');
      throw Exception('Failed to store refresh token: $e');
    }
  }

  @override
  Future<void> storeSessionData(Map<String, dynamic> sessionData) async {
    try {
      // Add timestamp to session data
      final enhancedSessionData = {
        ...sessionData,
        'stored_at': DateTime.now().toIso8601String(),
        'session_extended': true,
      };

      await _secureStorage.write(
        key: SimpleSecureStorageConfig.sessionDataKey,
        value: jsonEncode(enhancedSessionData),
      );

      debugPrint('✅ Session data stored successfully');
    } catch (e) {
      debugPrint('❌ Failed to store session data: $e');
      throw Exception('Failed to store session data: $e');
    }
  }

  // ---------------------------------------------------------------------------
  // Token Retrieval Operations
  // ---------------------------------------------------------------------------

  @override
  Future<String?> getToken() async {
    try {
      final token = await _secureStorage.read(
        key: SimpleSecureStorageConfig.accessTokenKey,
      );

      if (token != null) {
        // Check if token is expired before returning
        if (await isTokenExpired()) {
          debugPrint('⚠️ Token found but expired, clearing expired tokens');
          await clearExpiredTokens();
          return null;
        }
        
        debugPrint('✅ Valid token retrieved from storage');
        return token;
      }

      debugPrint('ℹ️ No token found in storage');
      return null;
    } catch (e) {
      debugPrint('❌ Failed to get token: $e');
      return null;
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(
        key: SimpleSecureStorageConfig.refreshTokenKey,
      );

      if (refreshToken != null) {
        debugPrint('✅ Refresh token retrieved from storage');
      } else {
        debugPrint('ℹ️ No refresh token found in storage');
      }

      return refreshToken;
    } catch (e) {
      debugPrint('❌ Failed to get refresh token: $e');
      return null;
    }
  }

  @override
  Future<Map<String, dynamic>> getSessionData() async {
    try {
      final sessionDataString = await _secureStorage.read(
        key: SimpleSecureStorageConfig.sessionDataKey,
      );

      if (sessionDataString != null) {
        final sessionData = jsonDecode(sessionDataString) as Map<String, dynamic>;
        debugPrint('✅ Session data retrieved from storage');
        return sessionData;
      }

      debugPrint('ℹ️ No session data found in storage');
      return {};
    } catch (e) {
      debugPrint('❌ Failed to get session data: $e');
      return {};
    }
  }

  @override
  Future<DateTime?> getTokenExpiry() async {
    try {
      final expiryString = await _secureStorage.read(
        key: SimpleSecureStorageConfig.tokenExpiryKey,
      );

      if (expiryString != null) {
        final expiry = DateTime.parse(expiryString);
        debugPrint('✅ Token expiry retrieved: $expiry');
        return expiry;
      }

      debugPrint('ℹ️ No token expiry found in storage');
      return null;
    } catch (e) {
      debugPrint('❌ Failed to get token expiry: $e');
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Token Validation Operations
  // ---------------------------------------------------------------------------

  @override
  Future<bool> hasValidToken() async {
    try {
      final token = await _secureStorage.read(
        key: SimpleSecureStorageConfig.accessTokenKey,
      );

      if (token == null || token.isEmpty) {
        debugPrint('ℹ️ No token found');
        return false;
      }

      // Check if token is expired
      if (await isTokenExpired()) {
        debugPrint('⚠️ Token found but expired');
        await clearExpiredTokens();
        return false;
      }

      debugPrint('✅ Valid token found');
      return true;
    } catch (e) {
      debugPrint('❌ Error checking token validity: $e');
      return false;
    }
  }

  @override
  Future<bool> isTokenExpired() async {
    try {
      final expiryDate = await getTokenExpiry();
      if (expiryDate == null) {
        // If no expiry date, assume token is valid for extended period
        debugPrint('ℹ️ No expiry date found, assuming token is valid');
        return false;
      }

      final now = DateTime.now();
      final isExpired = now.isAfter(expiryDate);

      if (isExpired) {
        final expiredSince = now.difference(expiryDate);
        debugPrint('⚠️ Token expired ${expiredSince.inDays} days ago');
      } else {
        final timeUntilExpiry = expiryDate.difference(now);
        debugPrint('✅ Token valid for ${timeUntilExpiry.inDays} days, ${timeUntilExpiry.inHours % 24} hours');
      }

      return isExpired;
    } catch (e) {
      debugPrint('❌ Error checking token expiry: $e');
      return true; // Assume expired on error for security
    }
  }

  // ---------------------------------------------------------------------------
  // Cleanup Operations
  // ---------------------------------------------------------------------------

  @override
  Future<void> clearExpiredTokens() async {
    try {
      if (await isTokenExpired()) {
        await Future.wait([
          _secureStorage.delete(key: SimpleSecureStorageConfig.accessTokenKey),
          _secureStorage.delete(key: SimpleSecureStorageConfig.tokenExpiryKey),
        ]);
        debugPrint('🧹 Expired tokens cleared');
      }
    } catch (e) {
      debugPrint('❌ Failed to clear expired tokens: $e');
    }
  }

  @override
  Future<void> clearAllData() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: SimpleSecureStorageConfig.accessTokenKey),
        _secureStorage.delete(key: SimpleSecureStorageConfig.refreshTokenKey),
        _secureStorage.delete(key: SimpleSecureStorageConfig.tokenExpiryKey),
        _secureStorage.delete(key: SimpleSecureStorageConfig.sessionDataKey),
        _secureStorage.delete(key: SimpleSecureStorageConfig.lastLoginKey),
      ]);

      debugPrint('🧹 All authentication data cleared');
    } catch (e) {
      debugPrint('❌ Failed to clear all data: $e');
    }
  }

  // ---------------------------------------------------------------------------
  // Additional Helper Methods
  // ---------------------------------------------------------------------------

  /// Extend session automatically for better user experience
  Future<void> extendSession() async {
    try {
      final token = await getToken();
      if (token != null) {
        // Extend token expiry
        final newExpiry = SessionPersistenceConfig.getAccessTokenExpiryFromNow();
        await _secureStorage.write(
          key: SimpleSecureStorageConfig.tokenExpiryKey,
          value: newExpiry.toIso8601String(),
        );

        // Update last login
        await _secureStorage.write(
          key: SimpleSecureStorageConfig.lastLoginKey,
          value: DateTime.now().toIso8601String(),
        );

        debugPrint('✅ Session extended until: $newExpiry');
      }
    } catch (e) {
      debugPrint('❌ Failed to extend session: $e');
    }
  }

  /// Get last login timestamp
  Future<DateTime?> getLastLogin() async {
    try {
      final lastLoginString = await _secureStorage.read(
        key: SimpleSecureStorageConfig.lastLoginKey,
      );

      if (lastLoginString != null) {
        return DateTime.parse(lastLoginString);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Failed to get last login: $e');
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Interface Implementation (Stubs)
  // ---------------------------------------------------------------------------

  @override
  Future<bool> hasValidRefreshToken() async {
    final refreshToken = await getRefreshToken();
    return refreshToken != null && refreshToken.isNotEmpty;
  }

  @override
  Future<bool> isTokenValid(String token) async {
    final storedToken = await getToken();
    return storedToken == token && !await isTokenExpired();
  }

  @override
  Future<Duration?> getTimeUntilExpiry() async {
    final expiryDate = await getTokenExpiry();
    if (expiryDate == null) return null;

    final now = DateTime.now();
    if (now.isAfter(expiryDate)) return Duration.zero;

    return expiryDate.difference(now);
  }

  @override
  Future<void> storeTokenExpiry(DateTime expiryDate) async {
    await _secureStorage.write(
      key: SimpleSecureStorageConfig.tokenExpiryKey,
      value: expiryDate.toIso8601String(),
    );
  }

  @override
  Future<void> updateTokenExpiry(DateTime newExpiryDate) async {
    await storeTokenExpiry(newExpiryDate);
  }

  @override
  Future<void> rotateTokens({
    required String newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  }) async {
    await storeToken(newToken, expiryDate: newExpiryDate);
    if (newRefreshToken != null) {
      await storeRefreshToken(newRefreshToken);
    }
  }

  @override
  Future<bool> get supportsBiometrics async => false;

  @override
  Future<bool> validateStorageIntegrity() async => true;

  @override
  bool get isEncrypted => true; // Flutter Secure Storage provides encryption

  @override
  Future<Map<String, dynamic>> getStorageMetadata() async {
    try {
      final lastLogin = await getLastLogin();
      final tokenExpiry = await getTokenExpiry();

      return {
        'storage_type': 'simple_secure',
        'encrypted': isEncrypted,
        'last_login': lastLogin?.toIso8601String(),
        'token_expiry': tokenExpiry?.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ Failed to get storage metadata: $e');
      return {};
    }
  }
}

/// Provider for simple secure token storage
@riverpod
SimpleSecureTokenStorage simpleSecureTokenStorage(Ref ref) {
  return SimpleSecureTokenStorage();
}
